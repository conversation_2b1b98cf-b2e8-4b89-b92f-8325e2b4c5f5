import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ianG<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  ResponsiveC<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'recharts';
import { Box, Typography, Grid, Paper, Chip } from '@mui/material';

const COLORS = {
  DISPONIBILE: '#2e7d32',
  IN_USO: '#ed6c02',
  TERMINATA: '#d32f2f',
  OVER: '#9c27b0'
};

const STATUS_COLORS = {
  primary: '#1976d2',
  secondary: '#dc004e',
  success: '#2e7d32',
  warning: '#ed6c02',
  info: '#0288d1',
  error: '#d32f2f'
};

const BobineChart = ({ data }) => {
  if (!data || !data.bobine) return null;

  // Raggruppa bobine per stato
  const bobinePerStato = data.bobine.reduce((acc, bobina) => {
    const stato = bobina.stato || 'SCONOSCIUTO';
    if (!acc[stato]) {
      acc[stato] = { count: 0, metri_totali: 0, metri_residui: 0 };
    }
    acc[stato].count++;
    acc[stato].metri_totali += bobina.metri_totali || 0;
    acc[stato].metri_residui += bobina.metri_residui || 0;
    return acc;
  }, {});

  const statoData = Object.entries(bobinePerStato).map(([stato, info]) => ({
    stato,
    count: info.count,
    metri_totali: info.metri_totali,
    metri_residui: info.metri_residui,
    metri_utilizzati: info.metri_totali - info.metri_residui,
    percentuale_utilizzo: info.metri_totali > 0 ? ((info.metri_totali - info.metri_residui) / info.metri_totali * 100) : 0
  }));

  // Raggruppa bobine per tipologia
  const bobinePerTipologia = data.bobine.reduce((acc, bobina) => {
    const tipologia = bobina.tipologia || 'SCONOSCIUTA';
    if (!acc[tipologia]) {
      acc[tipologia] = { count: 0, metri_totali: 0, metri_residui: 0 };
    }
    acc[tipologia].count++;
    acc[tipologia].metri_totali += bobina.metri_totali || 0;
    acc[tipologia].metri_residui += bobina.metri_residui || 0;
    return acc;
  }, {});

  const tipologiaData = Object.entries(bobinePerTipologia).map(([tipologia, info]) => ({
    tipologia: tipologia.length > 10 ? tipologia.substring(0, 10) + '...' : tipologia,
    tipologia_full: tipologia,
    count: info.count,
    metri_totali: info.metri_totali,
    metri_residui: info.metri_residui,
    metri_utilizzati: info.metri_totali - info.metri_residui,
    percentuale_utilizzo: info.metri_totali > 0 ? ((info.metri_totali - info.metri_residui) / info.metri_totali * 100) : 0
  }));

  // Dati per scatter plot efficienza
  const efficienzaData = data.bobine.map(bobina => ({
    id: bobina.id_bobina,
    metri_totali: bobina.metri_totali || 0,
    percentuale_utilizzo: bobina.percentuale_utilizzo || 0,
    stato: bobina.stato,
    tipologia: bobina.tipologia
  }));

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`${label}`}</Typography>
          {payload.map((entry, index) => (
            <Typography key={index} variant="body2" style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}`}
            </Typography>
          ))}
        </Paper>
      );
    }
    return null;
  };

  const ScatterTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Paper sx={{ p: 1, border: '1px solid #ccc' }}>
          <Typography variant="body2">{`ID: ${data.id}`}</Typography>
          <Typography variant="body2">{`Tipologia: ${data.tipologia}`}</Typography>
          <Typography variant="body2">{`Metri Totali: ${data.metri_totali}m`}</Typography>
          <Typography variant="body2">{`Utilizzo: ${data.percentuale_utilizzo.toFixed(1)}%`}</Typography>
          <Chip label={data.stato} size="small" />
        </Paper>
      );
    }
    return null;
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }) => {
    if (percent < 0.05) return null;

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text 
        x={x} 
        y={y} 
        fill="white" 
        textAnchor={x > cx ? 'start' : 'end'} 
        dominantBaseline="central"
        fontSize="12"
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        Analisi Grafiche Bobine ({data.totale_bobine} totali)
      </Typography>

      <Grid container spacing={3}>
        {/* Grafico a torta - Bobine per Stato */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 175 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Distribuzione per Stato
            </Typography>
            <ResponsiveContainer width="100%" height={140}>
              <PieChart>
                <Pie
                  data={statoData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {statoData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[entry.stato] || STATUS_COLORS.info} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico a barre - Utilizzo per Tipologia */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 175 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Utilizzo per Tipologia
            </Typography>
            <ResponsiveContainer width="100%" height={140}>
              <BarChart data={tipologiaData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="tipologia" angle={-45} textAnchor="end" height={80} />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="metri_totali" fill={STATUS_COLORS.primary} name="Metri Totali" />
                <Bar dataKey="metri_utilizzati" fill={STATUS_COLORS.success} name="Metri Utilizzati" />
                <Bar dataKey="metri_residui" fill={STATUS_COLORS.warning} name="Metri Residui" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Grafico a barre - Conteggio per Stato */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 175 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Numero Bobine per Stato
            </Typography>
            <ResponsiveContainer width="100%" height={140}>
              <BarChart data={statoData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="stato" />
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="count" fill={STATUS_COLORS.secondary} name="Numero Bobine" />
              </BarChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Scatter Plot - Efficienza Bobine */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2, height: 175 }}>
            <Typography variant="subtitle1" gutterBottom align="center">
              Efficienza Utilizzo (Metri vs Percentuale)
            </Typography>
            <ResponsiveContainer width="100%" height={140}>
              <ScatterChart margin={{ top: 20, right: 20, bottom: 20, left: 20 }}>
                <CartesianGrid />
                <XAxis 
                  type="number" 
                  dataKey="metri_totali" 
                  name="Metri Totali"
                  label={{ value: 'Metri Totali', position: 'insideBottom', offset: -10 }}
                />
                <YAxis 
                  type="number" 
                  dataKey="percentuale_utilizzo" 
                  name="Utilizzo %"
                  label={{ value: 'Utilizzo %', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip content={<ScatterTooltip />} />
                <Scatter 
                  name="Bobine" 
                  data={efficienzaData} 
                  fill={STATUS_COLORS.info}
                />
              </ScatterChart>
            </ResponsiveContainer>
          </Paper>
        </Grid>

        {/* Statistiche Riassuntive */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Statistiche Riassuntive
            </Typography>
            <Grid container spacing={2}>
              {statoData.map((stato, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Box sx={{ textAlign: 'center', p: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                    <Chip 
                      label={stato.stato} 
                      color={stato.stato === 'DISPONIBILE' ? 'success' : stato.stato === 'TERMINATA' ? 'error' : 'warning'}
                      sx={{ mb: 1 }}
                    />
                    <Typography variant="h6">{stato.count}</Typography>
                    <Typography variant="body2">bobine</Typography>
                    <Typography variant="body2">
                      {stato.metri_residui.toFixed(0)}m residui
                    </Typography>
                    <Typography variant="body2">
                      {stato.percentuale_utilizzo.toFixed(1)}% utilizzo
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default BobineChart;
