#!/usr/bin/env python
# run_system_simple.py - Versione semplificata dello script di avvio
import subprocess
import sys
import os
import time
import signal
from pathlib import Path

def run_backend():
    """Avvia il server FastAPI (backend)"""
    print("Avvio del backend...")

    # Ottieni il percorso assoluto della directory backend
    backend_dir = Path(__file__).resolve().parent / "backend"

    # Verifica che la directory esista
    if not backend_dir.exists():
        print(f"Errore: La directory del backend non esiste: {backend_dir}")
        return None

    # Cambia directory
    os.chdir(backend_dir)

    # Comando per avviare il backend
    cmd = [sys.executable, "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port=8001"]
    print(f"Esecuzione comando: {' '.join(cmd)}")

    try:
        # Avvia il processo senza reindirizzare l'output
        process = subprocess.Popen(cmd)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(3)
        print("Backend avviato con successo!")
        return process
    except Exception as e:
        print(f"Errore durante l'avvio del backend: {e}")
        return None

def run_frontend(backend_port=8001):
    """Avvia il server React (frontend)"""
    print("Avvio del frontend...")

    # Ottieni il percorso assoluto della directory frontend
    frontend_dir = Path(__file__).resolve().parent / "frontend"

    # Verifica che la directory esista
    if not frontend_dir.exists():
        print(f"Errore: La directory del frontend non esiste: {frontend_dir}")
        return None

    # Cambia directory
    os.chdir(frontend_dir)

    # Comando per avviare il frontend
    cmd = f"npm start"
    print(f"Esecuzione comando: {cmd}")

    try:
        # Avvia il processo senza reindirizzare l'output
        process = subprocess.Popen(cmd, shell=True)

        # Attendi un po' per assicurarsi che il server si avvii
        time.sleep(5)
        print("Frontend avviato con successo!")
        return process
    except Exception as e:
        print(f"Errore durante l'avvio del frontend: {e}")
        return None

def initialize_bobina_vuota():
    """Inizializza il record BOBINA_VUOTA nella tabella parco_cavi se non esiste già"""
    print("Verifica e inizializzazione del record BOBINA_VUOTA...")

    # Importa i moduli necessari
    sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
    from scripts.add_bobina_vuota import add_bobina_vuota

    # Esegui la funzione di inizializzazione
    try:
        result = add_bobina_vuota()
        if result:
            print("Record BOBINA_VUOTA verificato con successo.")
        else:
            print("Errore durante l'inizializzazione del record BOBINA_VUOTA.")
    except Exception as e:
        print(f"Errore durante l'inizializzazione del record BOBINA_VUOTA: {e}")

def fix_bobina_vuota():
    """Corregge i cavi non posati che hanno erroneamente id_bobina = 'BOBINA_VUOTA'."""
    print("Correzione dei cavi non posati con id_bobina errato...")

    # Importa i moduli necessari
    sys.path.insert(0, str(Path(__file__).resolve().parent.parent))
    from scripts.fix_bobina_vuota import fix_bobina_vuota as fix_function

    # Esegui la funzione di correzione
    try:
        result = fix_function()
        if result:
            print("Correzione dei cavi completata con successo.")
        else:
            print("Errore durante la correzione dei cavi.")
    except Exception as e:
        print(f"Errore durante la correzione dei cavi: {e}")

def main():
    """Funzione principale"""
    print("\n=== Avvio del sistema CMS ===\n")

    # Inizializza il record BOBINA_VUOTA
    initialize_bobina_vuota()

    # Correggi i cavi non posati con id_bobina errato
    fix_bobina_vuota()

    # Salva la directory corrente
    original_dir = os.getcwd()

    # Avvia il backend
    backend_process = run_backend()
    if not backend_process:
        print("Errore: Impossibile avviare il backend.")
        return

    # Torna alla directory originale
    os.chdir(original_dir)

    # Avvia il frontend
    frontend_process = run_frontend()
    if not frontend_process:
        print("Errore: Impossibile avviare il frontend.")
        # Termina il backend
        backend_process.terminate()
        return

    # Torna alla directory originale
    os.chdir(original_dir)

    print("\n=== Sistema CMS avviato con successo! ===\n")
    print("Backend: http://localhost:8002")
    print("Frontend: http://localhost:3000")
    print("\nPremi Ctrl+C per terminare entrambi i server")

    # Gestione del segnale di interruzione
    def signal_handler(sig, frame):
        print("\nTerminazione dei server in corso...")
        if frontend_process:
            frontend_process.terminate()
        if backend_process:
            backend_process.terminate()
        print("Server terminati. Arrivederci!")
        sys.exit(0)

    # Registra il gestore del segnale
    signal.signal(signal.SIGINT, signal_handler)

    # Mantiene il programma in esecuzione
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # Questo blocco non dovrebbe essere mai raggiunto grazie al signal_handler
        pass

if __name__ == "__main__":
    main()
