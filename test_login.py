#!/usr/bin/env python3
"""Script per testare l'autenticazione del sistema CMS"""

import requests
import json

def test_admin_login():
    """Testa il login dell'admin"""
    url = "http://localhost:8001/api/auth/login"

    # Dati per il login (FormData come richiesto dall'endpoint OAuth2)
    data = {
        'username': 'admin',
        'password': 'admin'
    }

    try:
        print("🔄 Tentativo di login admin...")
        response = requests.post(url, data=data)

        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 200:
            print("✅ Login admin riuscito!")
            return response.json()
        else:
            print("❌ Login admin fallito!")
            return None

    except Exception as e:
        print(f"❌ Errore durante il test di login: {str(e)}")
        return None

def test_cantiere_login():
    """Testa il login di un cantiere (se esiste)"""
    url = "http://localhost:8001/api/auth/login/cantiere"

    # Testa diversi cantieri dal database
    cantieri_test = [
        {'codice_univoco': 'D56440D8', 'password': 'admin'},
        {'codice_univoco': 'TN387XF', 'password': 'test'},
        {'codice_univoco': 'YA513AO', 'password': 'test1'},
        {'codice_univoco': 'ZJ863XI', 'password': 'admin'}
    ]

    for data in cantieri_test:
        try:
            print(f"🔄 Tentativo di login cantiere {data['codice_univoco']} con password {data['password']}...")
            response = requests.post(url, json=data)

            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")

            if response.status_code == 200:
                print("✅ Login cantiere riuscito!")
                return response.json()
            else:
                print("❌ Login cantiere fallito!")

        except Exception as e:
            print(f"❌ Errore durante il test di login cantiere: {str(e)}")

    return None

if __name__ == "__main__":
    print("=== Test Autenticazione Sistema CMS ===\n")

    # Test login admin
    admin_result = test_admin_login()
    print()

    # Test login cantiere
    cantiere_result = test_cantiere_login()
    print()

    print("=== Risultati ===")
    print(f"Admin login: {'✅ OK' if admin_result else '❌ FAILED'}")
    print(f"Cantiere login: {'✅ OK' if cantiere_result else '❌ FAILED'}")
