#!/usr/bin/env python3
"""
Test rapido per verificare che l'endpoint /cavi/{cantiere_id}/installati funzioni correttamente.
"""

import requests
import json

API_URL = "http://localhost:8001/api"

def test_endpoint_installati():
    """Testa l'endpoint per i cavi installati."""
    print("=== TEST ENDPOINT CAVI INSTALLATI ===")

    # Test senza autenticazione per verificare che l'endpoint esista
    cantiere_id = 2
    url = f"{API_URL}/cavi/{cantiere_id}/installati"

    print(f"Testando URL: {url}")

    try:
        response = requests.get(url)
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")

        if response.status_code == 401:
            print("✅ Endpoint trovato! (Errore 401 = richiede autenticazione)")
            print("✅ L'errore 'Cavo con ID installati non trovato' è stato risolto!")
            return True
        elif response.status_code == 404:
            print("❌ Endpoint non trovato (404)")
            print("❌ L'errore persiste")
            return False
        else:
            print(f"Status code inaspettato: {response.status_code}")
            print(f"Response: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ Impossibile connettersi al server")
        return False
    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        return False

def test_endpoint_cavo_singolo():
    """Testa che l'endpoint per cavo singolo funzioni ancora."""
    print("\n=== TEST ENDPOINT CAVO SINGOLO ===")

    cantiere_id = 2
    cavo_id = "00ACA1013BXRA"  # Un cavo che dovrebbe esistere
    url = f"{API_URL}/cavi/{cantiere_id}/{cavo_id}"

    print(f"Testando URL: {url}")

    try:
        response = requests.get(url)
        print(f"Status code: {response.status_code}")

        if response.status_code == 401:
            print("✅ Endpoint cavo singolo funziona! (Errore 401 = richiede autenticazione)")
            return True
        elif response.status_code == 404:
            print("⚠️ Cavo non trovato o endpoint non funziona")
            return False
        else:
            print(f"Status code inaspettato: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Errore durante il test: {str(e)}")
        return False

if __name__ == "__main__":
    print("Verifica che il backend sia in esecuzione su http://localhost:8001")

    # Test endpoint installati
    test1_ok = test_endpoint_installati()

    # Test endpoint cavo singolo
    test2_ok = test_endpoint_cavo_singolo()

    print("\n=== RISULTATO FINALE ===")
    if test1_ok:
        print("✅ Problema risolto: l'endpoint /installati ora funziona correttamente!")
    else:
        print("❌ Problema non risolto: l'endpoint /installati non funziona")

    if test2_ok:
        print("✅ L'endpoint per cavo singolo funziona ancora correttamente")
    else:
        print("⚠️ Possibile problema con l'endpoint per cavo singolo")
