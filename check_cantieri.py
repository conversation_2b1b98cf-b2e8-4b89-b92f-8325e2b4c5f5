#!/usr/bin/env python3
"""Script per verificare i cantieri nel database"""

from sqlalchemy import create_engine, text

# Configurazione del database
DB_HOST = "localhost"
DB_PORT = "5432"
DB_NAME = "cantieri"
DB_USER = "postgres"
DB_PASSWORD = "Taranto"

# Stringa di connessione per SQLAlchemy
DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Crea l'engine SQLAlchemy
engine = create_engine(DATABASE_URL)

# Esegui una query per ottenere tutti i cantieri
with engine.connect() as connection:
    result = connection.execute(text("SELECT * FROM cantieri"))
    cantieri = result.fetchall()
    
    print("Cantieri nel database:")
    if cantieri:
        for cantiere in cantieri:
            print(f"ID: {cantiere.id_cantiere}, Nome: {cantiere.nome}, Codice: {cantiere.codice_univoco}, Password: {cantiere.password_cantiere}")
    else:
        print("Nessun cantiere trovato nel database.")
